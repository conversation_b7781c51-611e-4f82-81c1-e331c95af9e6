package file

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/os/gfile"
	v1 "halalplus/app/file-storage-svc/api/v1"
	"halalplus/app/file-storage-svc/internal/consts"
	"halalplus/app/file-storage-svc/internal/errno"
	"halalplus/app/file-storage-svc/internal/model"
	"halalplus/app/file-storage-svc/internal/service"
	"path/filepath"
	"strings"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

const maxSize = 2 * 1024 * 1024 // 2 MB

// Upload 上传文件
func (a *Controller) Upload(ctx context.Context, req *v1.FileUploadReq) (res *v1.FileUploadRes, err error) {
	if req.File == nil {
		return nil, gerror.NewCode(gcode.CodeMissingParameter, gi18n.T(ctx, "upload.file.missingParameter"))
	}

	if req.File.Size > maxSize {
		return nil, gerror.NewCode(gcode.CodeValidationFailed,
			gi18n.T(ctx, "upload.file.missingParameter"))
	}

	ext := strings.ToLower(filepath.Ext(req.File.Filename))
	allowed := map[string]struct{}{
		".jpg": {}, ".jpeg": {}, ".png": {}, ".webp": {}, ".gif": {},
	}
	if _, ok := allowed[ext]; !ok {
		return nil, gerror.NewCode(gcode.CodeValidationFailed,
			gi18n.T(ctx, "upload.file.missingParameter"))
	}

	f, err := req.File.Open()
	if err != nil {
		g.Log().Error(ctx, err)
		err = errno.T(ctx, errno.CodeFileUploadError)
		return
	}
	defer func() {
		_ = f.Close()
	}()
	// 判断文件目录前缀
	_, ok := consts.PrefixName[req.FilePrefix]
	if !ok {
		req.FilePrefix = consts.FilePrefixPublicDir
	}
	// 判断文件模块
	_, ok = consts.ModuleName[req.FilePrefix]
	if !ok {
		req.FileModule = consts.FileModuleDefault
	}

	result, err := service.File().SingleUpload(ctx, model.SingleUploadInput{
		File: f,
		//Name:       req.File.Filename,
		NameExt:    gfile.Ext(req.File.Filename),
		Prefix:     req.FilePrefix,
		ModuleName: req.FileModule,
	})
	if err != nil {
		return nil, err
	}
	res = &v1.FileUploadRes{
		Name:       req.File.Filename,
		Key:        result.ObjectName,
		BackendUrl: service.File().GetBackUpUrl(ctx, result.ObjectName),
	}
	return res, nil
}

func (a *Controller) MultiUpload(ctx context.Context, req *v1.MultiUploadReq) (res *v1.MultiUploadRes, err error) {
	if len(req.File) == 0 {
		return nil, gerror.NewCode(gcode.CodeMissingParameter, gi18n.T(ctx, "upload.file.missingParameter"))
	}
	list := make([]*v1.FileUploadRes, len(req.File))
	for key, item := range req.File {
		upload, err := a.Upload(ctx, &v1.FileUploadReq{File: item, FilePrefix: req.FilePrefix, FileModule: req.FileModule})
		if err != nil {
			return nil, err
		}
		list[key] = upload
	}
	return &v1.MultiUploadRes{
		List: list,
	}, nil
}
