syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";
import "google/protobuf/wrappers.proto";

// FAQ分类列表请求
message FaqCateListReq {
  uint32 language_id = 1;   // 语言id
}

// FAQ分类列表数据
message FaqCateListData {
  repeated FaqCateItem list = 1;
}

// FAQ分类列表响应
message FaqCateListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  FaqCateListData data = 4; // 分类列表
}


// FAQ分类列表item
message FaqCateItem {
  uint32 id         = 1;  // 分类ID
  int32  is_open    = 2;  // 是否启用
  int32  sort       = 3;  // 排序
  int32  cate_count = 4;  // 该分类下的FAQ数量
  string title      = 5;  // 分类名称
  uint32  language_id = 6;// 语言id
}

// FAQ列表by分类ID请求
message FaqListByCateIdReq {
  uint32 language_id = 1;   // 语言id
  uint32 cate_id = 2;       // 分类id
  string keyword = 3;       // 搜索关键字
  common.PageRequest page = 4;  // 分页参数
  uint32 position_id = 5;// 位置id 1 我的(关于) 2 信仰(朝觐) 3 信仰(副朝)
}

message FaqListByCateIdData {
  repeated  FaqQuestionItem list = 1;
  common.PageResponse page = 2;  // 分页参数
}

// FAQ列表by分类ID响应
message FaqListByCateIdRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  FaqListByCateIdData data = 4; // FAQ列表
}

// FAQ问题item
message FaqQuestionItem {
  uint32 id         = 1;    // 问题ID
  int32  is_open    = 2;    // 是否启用
  int32  sort       = 3;    // 排序
  int32  views      = 4;    // 浏览次数
  string title      = 5;    // 问题标题
  string desc       = 6;    // 问题描述
  int32  language_id = 7;   // 语言id
  uint32 faq_cate_id = 8;   // 分类id
  uint64 publish_time = 9;  //发布时间
}

// FAQ详情请求
message FaqOneReq {
  uint32 language_id = 1;   // 语言id
  uint32 id = 2; // 分类id
}

// FAQ详情响应
message FaqOneRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  FaqQuestionOneItem data = 5;
}

// FAQ问题详情item
message FaqQuestionOneItem{
  uint32 id         = 1;  // 问题ID
  int32  is_open    = 2;  // 是否启用
  int32  sort       = 3;  // 排序
  int32  views      = 4;  // 浏览次数
  string title      = 5;  // 问题标题
  string desc       = 6;  // 问题描述
  int32  language_id = 7; // 语言id
  uint32 faq_cate_id = 8; // 分类id
  string faq_cate_title = 9;// 分类名称
  uint64 publish_time = 10;  //发布时间
}

message FaqMediaReq {

}

message FaqMediaRes {
    int32 code = 1;
    string msg = 2;
    common.Error error = 3;
    FaqMediaData data = 4;
}

message FaqMediaData {
  repeated FaqMediaItem list = 1;
}

message FaqMediaItem {
    uint32 id = 1;
    string icon_url = 2;
    string name = 3;
    string jump_url = 4;

}

message FaqListReq {
  common.PageRequest page = 1;  // 分页参数
  uint32 position_id = 2;// 位置id  2 信仰(朝觐) 3 信仰(副朝)
  string keyword = 3;       // 搜索关键字
}


service FaqService {
  // FAQ分类列表
  rpc FaqCategoryList(FaqCateListReq) returns (FaqCateListRes);
  // FAQ列表通过分类ID
  rpc FaqListByCateId(FaqListByCateIdReq) returns (FaqListByCateIdRes);
  // FAQ详情
  rpc FaqOne(FaqOneReq) returns (FaqOneRes);
  // 社交媒体
  rpc FaqMedia(FaqMediaReq) returns (FaqMediaRes);
  // FAQ列表无分类ID
  rpc FaqList(FaqListReq) returns(FaqListByCateIdRes);
}




